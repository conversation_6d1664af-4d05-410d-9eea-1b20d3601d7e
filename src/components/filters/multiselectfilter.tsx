"use client"

import { useState } from "react"
import { CalendarIcon, ChevronDown, X, Filter } from "lucide-react"
import { format } from "date-fns"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

const statusOptions = [
  { id: "active", label: "Active" },
  { id: "inactive", label: "Inactive" },
  { id: "pending", label: "Pending" },
  { id: "suspended", label: "Suspended" },
  { id: "completed", label: "Completed" },
]

const districtOptions = [
  { id: "north", label: "North District" },
  { id: "south", label: "South District" },
  { id: "east", label: "East District" },
  { id: "west", label: "West District" },
  { id: "central", label: "Central District" },
  { id: "downtown", label: "Downtown District" },
  { id: "suburban", label: "Suburban District" },
  { id: "industrial", label: "Industrial District" },
]

interface FilterState {
  status: string[]
  districts: string[]
  startDate: Date | undefined
  endDate: Date | undefined
}

export default function Multiselectfilter() {
  const [pendingFilters, setPendingFilters] = useState<FilterState>({
    status: [],
    districts: [],
    startDate: undefined,
    endDate: undefined,
  })

  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    status: [],
    districts: [],
    startDate: undefined,
    endDate: undefined,
  })
  const [open, setOpen] = useState(false)

  const handleStatusChange = (statusId: string, checked: boolean) => {
    if (checked) {
      setPendingFilters((prev) => ({ ...prev, status: [...prev.status, statusId] }))
    } else {
      setPendingFilters((prev) => ({ ...prev, status: prev.status.filter((id) => id !== statusId) }))
    }
  }

  const handleDistrictChange = (districtId: string, checked: boolean) => {
    if (checked) {
      setPendingFilters((prev) => ({ ...prev, districts: [...prev.districts, districtId] }))
    } else {
      setPendingFilters((prev) => ({ ...prev, districts: prev.districts.filter((id) => id !== districtId) }))
    }
  }

  const applyFilters = () => {
    setAppliedFilters(pendingFilters)
    setOpen(false)
  }

  const resetFilters = () => {
    const emptyFilters = {
      status: [],
      districts: [],
      startDate: undefined,
      endDate: undefined,
    }
    setPendingFilters(emptyFilters)
    setAppliedFilters(emptyFilters)
    setOpen(false)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    count += appliedFilters.status.length
    count += appliedFilters.districts.length
    if (appliedFilters.startDate) count += 1
    if (appliedFilters.endDate) count += 1
    return count
  }

  const getSelectedLabels = (ids: string[], options: { id: string; label: string }[]) => {
    return options.filter((option) => ids.includes(option.id)).map((option) => option.label)
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className="w-full max-w-2xl space-y-4">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-transparent"
          >
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              {activeFiltersCount === 0 ? (
                <span className="text-muted-foreground">Apply filters...</span>
              ) : (
                <span>{`${activeFiltersCount} filter${activeFiltersCount > 1 ? "s" : ""} applied`}</span>
              )}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="p-6 space-y-6 max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold leading-none">Filters</h4>
              {activeFiltersCount > 0 && <></>}
            </div>

            {/* Status Filter */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Status</Label>
              <div className="grid grid-cols-2 gap-3">
                {statusOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${option.id}`}
                      checked={pendingFilters.status.includes(option.id)}
                      onCheckedChange={(checked) => handleStatusChange(option.id, checked as boolean)}
                    />
                    <Label htmlFor={`status-${option.id}`} className="text-sm font-normal cursor-pointer">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* District Filter */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">District</Label>
              <div className="grid grid-cols-2 gap-3">
                {districtOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`district-${option.id}`}
                      checked={pendingFilters.districts.includes(option.id)}
                      onCheckedChange={(checked) => handleDistrictChange(option.id, checked as boolean)}
                    />
                    <Label htmlFor={`district-${option.id}`} className="text-sm font-normal cursor-pointer">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Date Range Filter */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Date Range</Label>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !pendingFilters.startDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {pendingFilters.startDate ? format(pendingFilters.startDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={pendingFilters.startDate}
                        onSelect={(date) => setPendingFilters((prev) => ({ ...prev, startDate: date }))}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !pendingFilters.endDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {pendingFilters.endDate ? format(pendingFilters.endDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={pendingFilters.endDate}
                        onSelect={(date) => setPendingFilters((prev) => ({ ...prev, endDate: date }))}
                        initialFocus
                        disabled={(date) => (pendingFilters.startDate ? date < pendingFilters.startDate : false)}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
            <Separator />

            <div className="flex gap-2 pt-2">
              <Button onClick={applyFilters} className="flex-1">
                Apply Filters
              </Button>
              <Button onClick={resetFilters} variant="outline" className="flex-1 bg-transparent">
                Reset
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Display active filters */}
      {activeFiltersCount > 0 && (
        <div className="space-y-3">
          <p className="text-sm font-medium">Active filters:</p>
          <div className="flex flex-wrap gap-2">
            {/* Status badges */}
            {getSelectedLabels(appliedFilters.status, statusOptions).map((label, index) => (
              <Badge key={`status-${index}`} variant="secondary" className="flex items-center gap-1">
                Status: {label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => {
                    const statusId = statusOptions.find((opt) => opt.label === label)?.id
                    if (statusId) {
                      const newStatus = appliedFilters.status.filter((id) => id !== statusId)
                      const newFilters = { ...appliedFilters, status: newStatus }
                      setAppliedFilters(newFilters)
                      setPendingFilters(newFilters)
                    }
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}

            {/* District badges */}
            {getSelectedLabels(appliedFilters.districts, districtOptions).map((label, index) => (
              <Badge key={`district-${index}`} variant="secondary" className="flex items-center gap-1">
                District: {label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => {
                    const districtId = districtOptions.find((opt) => opt.label === label)?.id
                    if (districtId) {
                      const newDistricts = appliedFilters.districts.filter((id) => id !== districtId)
                      const newFilters = { ...appliedFilters, districts: newDistricts }
                      setAppliedFilters(newFilters)
                      setPendingFilters(newFilters)
                    }
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}

            {/* Date badges */}
            {appliedFilters.startDate && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Start: {format(appliedFilters.startDate, "MMM dd, yyyy")}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => {
                    const newFilters = { ...appliedFilters, startDate: undefined }
                    setAppliedFilters(newFilters)
                    setPendingFilters(newFilters)
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}

            {appliedFilters.endDate && (
              <Badge variant="secondary" className="flex items-center gap-1">
                End: {format(appliedFilters.endDate, "MMM dd, yyyy")}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1 hover:bg-transparent"
                  onClick={() => {
                    const newFilters = { ...appliedFilters, endDate: undefined }
                    setAppliedFilters(newFilters)
                    setPendingFilters(newFilters)
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
