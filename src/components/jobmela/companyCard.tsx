import { Briefcase, Building2, Calendar, Edit, Eye, Mail, MapPin, MoreHorizontal, Phone, User } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../ui/dropdown-menu"
import { Button } from "../ui/button"
import { Separator } from "../ui/separator"
import { formatDate } from "@/lib/formateDate"


export function CompanyCard({ company }: { company: any }) {
    
  return (
    <Card className="hover:shadow-md transition-shadow duration-200 border-l-2">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <Building2 className="h-4 w-4 text-blue-600" />
              {company.companyName}
            </CardTitle>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="text-xs">
                ID: {company.pklEntityId}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {company.userName}
              </Badge>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit Company
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="truncate">{company.companyEmail}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-4 w-4 text-gray-400" />
            <span>{company.companyMobile}</span>
          </div>
        </div>

        {/* Address */}
        <div className="flex items-start gap-2 text-sm text-gray-600">
          <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
          <span>
            {company.companyAddress}, {company.companyPinCode}
          </span>
        </div>

        <Separator />

        {/* Organization Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Organization Type</p>
            <p className="text-sm text-gray-900 font-medium">{company.organizationTypeName}</p>
          </div>
          <div>
            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Employer Type</p>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-900 font-medium">{company.empTypeName}</span>
            </div>
          </div>
        </div>

        {/* Statistics and Date */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-2">
            <Briefcase className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-gray-900">{company.totalJobsPosted} Jobs Posted</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Calendar className="h-3 w-3" />
            <span>Joined {formatDate(company.createdAt)}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
            <Eye className="mr-2 h-4 w-4" />
            View Profile
          </Button>
          <Button size="sm" className="flex-1">
            <Briefcase className="mr-2 h-4 w-4" />
            View Jobs
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
