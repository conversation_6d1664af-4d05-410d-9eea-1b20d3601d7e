
import './App.css'
import { Signin } from './pages/Signin'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import  Dashboard  from './pages/Dashboard'
import DashboardLayout from './components/dashboardLayout'
import { JobMela } from './pages/JobMela/JobMela'
import { UserManageMent } from './pages/UserManageMent/UserManageMent'
import { Toaster } from 'sonner'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import JobMelaDetails from './pages/JobMela/JobMelaDetails'


function App() {
  const queryClient = new QueryClient()
  return (
  
    <QueryClientProvider client={queryClient}>
      <Toaster />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Signin />} />
           <Route path="/dashboard" element={<DashboardLayout />} >
            <Route index element={< Dashboard />} />
            <Route path="jobmela" element={< JobMela />} />
            <Route path="usermanagement" element={< UserManageMent />} />
            <Route path="jobmela/:id" element={< JobMelaDetails />} />
          </Route>
        </Routes>
      </BrowserRouter>

    </QueryClientProvider>
  )
}

export default App
