# useFetchQ<PERSON>y Hook

The `useFetchQuery` hook is a custom React hook that simplifies fetching data from an API using React Query. It provides a convenient way to handle data fetching, caching, and error handling.

## Usage

To use the `useFetchQuery` hook, you need to import it from the appropriate file. Here's an example of how to use it:

```jsx
import { useFetchQuery } from 'path/to/useFetchQuery';

function MyComponent() {
  const { data, isLoading, error } = useFetchQuery('https://api.example.com/data',{
    queryKey: ['data-list'],
    enabled: true,
    onSuccess: (data) => {
      console.log('Data fetched successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to fetch data:', error);
    },
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      {/* Render your data here */}
      {data.map((item) => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```