import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  User,
  MoreHorizontal,
  Edit,
  Eye,
  Clock,
  ChevronDown,
  ChevronUp,
  Users,

} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useEffect, useState } from "react"
import { usePostQuery } from "@/hooks/useFetchQuery"
import { Switch } from "@/components/ui/switch"
import { useMutation } from '@tanstack/react-query'
import { postData } from '@/utils/api/postApi'
import { useParams } from "react-router-dom"
import { useDispatch } from "react-redux"
import { setJobMelaDetails } from "@/store/feature/jobmela/jobmelaSlice"
import { NotFound } from "@/components/notfound"
import { toast } from 'sonner';
import { QueryClient } from "@tanstack/react-query"

function CompanyCard({ company }: { company: any }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <Card className="hover:shadow-md transition-shadow duration-200 border-l-2">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <Building2 className="h-4 w-4 text-blue-600" />
              {company.companyName}
            </CardTitle>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="text-xs">
                ID: {company.pklEntityId}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {company.userName}
              </Badge>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit Company
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="truncate">{company.companyEmail}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-4 w-4 text-gray-400" />
            <span>{company.companyMobile}</span>
          </div>
        </div>

        {/* Address */}
        <div className="flex items-start gap-2 text-sm text-gray-600">
          <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
          <span>
            {company.companyAddress}, {company.companyPinCode}
          </span>
        </div>

        <Separator />

        {/* Organization Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Organization Type</p>
            <p className="text-sm text-gray-900 font-medium">{company.organizationTypeName}</p>
          </div>
          <div>
            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Employer Type</p>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-900 font-medium">{company.empTypeName}</span>
            </div>
          </div>
        </div>

        {/* Statistics and Date */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-2">
            <Briefcase className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-gray-900">{company.totalJobsPosted} Jobs Posted</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Calendar className="h-3 w-3" />
            <span>Joined {formatDate(company.createdAt)}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
            <Eye className="mr-2 h-4 w-4" />
            View Profile
          </Button>
          <Button size="sm" className="flex-1">
            <Briefcase className="mr-2 h-4 w-4" />
            View Jobs
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

function MelaContainer({ melaInfo, companies }: { melaInfo: any; companies: any[] }) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [check, setCheck] = useState(melaInfo.bActive === 0)

  // Use mutation for API calls triggered by user actions
  const updateMelaMutation = useMutation({
    mutationFn: (payload: any) => postData({
      url: '/jobMela/verify',
      data: payload
    }),
    
    onSuccess: () => {
      console.log('Mela status updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update mela status:', error)
    }
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    })
  }

  const isActive = melaInfo.bActive === 1
  const startDate = new Date(melaInfo.start_date)
  const endDate = new Date(melaInfo.end_date)
  const currentDate = new Date()

  const getEventStatus = () => {
    if (!isActive) return { status: "Inactive", color: "bg-red-100 text-red-800" }
    if (currentDate < startDate) return { status: "Upcoming", color: "bg-blue-100 text-blue-800" }
    if (currentDate > endDate) return { status: "Completed", color: "bg-gray-100 text-gray-800" }
    return { status: "Active", color: "bg-green-100 text-green-800" }
  }

  const eventStatus = getEventStatus()
  const totalJobs = companies.reduce((sum, company) => sum + company.totalJobsPosted, 0)
  useEffect(() => {
  setCheck(melaInfo.bActive); // Sync when melaInfo changes
}, [melaInfo]);

  const handleCheck = () => {
    const newChecked = !check;
    setCheck(newChecked);
    console.log(newChecked)

    const payload = {
      "melaId": melaInfo.melaId,
      "bActive": newChecked ? 1 : 0,
      "vsRemarks": "Status updated"
    }
    
    updateMelaMutation.mutate(payload)
  };

  return (
    <div className="mb-8">
      {/* Mela Header Card */}
      <Card className="border-2 bg-gray-100 shadow-lg">
        <CardHeader className="pb-4">
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-3">
                  <div className="p-3 bg-blue-600 rounded-xl shadow-md">
                    <Building2 className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-3xl font-bold text-gray-900 mb-1">
                      {melaInfo.venueName} Job Mela
                    </CardTitle>
                    <p className="text-gray-600">
                      Event ID: {melaInfo.melaId} • {melaInfo.district}
                    </p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-3 mb-4">
                  <Badge className={`${eventStatus.color} border-0 px-3 py-1`}>{eventStatus.status}</Badge>
                  <Badge variant="outline" className="px-3 py-1">
                    <Users className="mr-1 h-3 w-3" />
                    {companies.length} Companies
                  </Badge>
                  <Badge variant="outline" className="px-3 py-1">
                    <Briefcase className="mr-1 h-3 w-3" />
                    {totalJobs} Job Openings
                  </Badge>
                </div>
              </div>

              <div className="flex items-center gap-2">
              
                 <Switch id="mela-status" 
                  onClick={handleCheck}
        checked={check}
                 />
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <span className="sr-only">Toggle details</span>
                  </Button>
                </CollapsibleTrigger>
              </div>
            </div>

            <CollapsibleContent className="space-y-4">
              {/* Event Description */}
              <div className="bg-white p-4 rounded-lg border border-blue-100">
         
                <p className="text-gray-700 leading-relaxed">{melaInfo.description}</p>
              </div>

              {/* Event Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Date Range */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Duration</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{formatDate(melaInfo.start_date)}</p>
                    <p className="text-xs text-gray-500">to</p>
                    <p className="text-sm font-medium text-gray-900">{formatDate(melaInfo.end_date)}</p>
                  </div>
                </div>

                {/* Location */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4 text-green-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Location</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{melaInfo.address}</p>
                    <p className="text-xs text-gray-600">{melaInfo.district}</p>
                  </div>
                </div>

                {/* Time Slots */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-orange-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Daily Hours</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{formatTime(melaInfo.dtSlotStartTime)}</p>
                    <p className="text-xs text-gray-500">to</p>
                    <p className="text-sm font-medium text-gray-900">{formatTime(melaInfo.dtSlotEndTime)}</p>
                  </div>
                </div>

                {/* Management */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Edit className="h-4 w-4 text-purple-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Last Updated</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{formatDate(melaInfo.dtUpdatedAt)}</p>
                    <p className="text-xs text-gray-600">Created: {formatDate(melaInfo.dtCreatedAt)}</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Building2 className="mr-2 h-4 w-4" />
                  Manage Mela
                </Button>
                <Button variant="outline">
                  <Eye className="mr-2 h-4 w-4" />
                  View Reports
                </Button>
                <Button variant="outline">
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule
                </Button>
               
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardHeader>
      </Card>

      {/* Companies Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-600" />
            Participating Companies ({companies.length})
          </h3>
          
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 pl-4 border-l-2 border-blue-100">
          {companies.map((company: any) => (
            <CompanyCard key={company.companyName} company={company} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default function AdminDashboard() {
  const  melaId  = useParams().id
  const payload = {
    melaId: melaId,
  }

  const {data, isLoading, isError} = usePostQuery({
    queryKey: ['/jobMela/CompanyList', JSON.stringify(payload)],
    payload,
    staleTime: 1000 * 60 * 5,
  });

  const dispatch = useDispatch();
  useEffect(() => {
    if (data?.data.melaInfo) {
      dispatch(setJobMelaDetails(data.data.melaInfo));
    }
  }, [data, dispatch]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError || !data) {
    return <div>
      <NotFound/>
    </div>;
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Job Mela Admin Dashboard</h1>
          <p className="text-gray-600 text-lg">Manage job mela events and participating companies</p>
        </div>
        {/* Mela Container with Companies */}
        <MelaContainer melaInfo={data.data.melaInfo} companies={data.data.companies} />
      </div>
    </div>
  )
}
