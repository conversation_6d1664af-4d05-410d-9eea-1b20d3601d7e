import { useState } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/ui/datatable';
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom';
import { Eye, Loader } from 'lucide-react';
import type { JobMelaItem } from '@/lib/types';
import { usePostQuery } from '@/hooks/useFetchQuery';
import { NotFound } from '@/components/notfound';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import Multiselectfilter from '@/components/filters/multiselectfilter';
import SearchBar from '@/components/ui/searchbar';
const PAGE_SIZE = 5;
export const JobMela = () => {
  const navigate = useNavigate()
  const [pageIndex, setPageIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const payload = {
    page: pageIndex + 1,
    limit: PAGE_SIZE,
    melaId: null,
    active: null,
    melaName: searchQuery,
    district: '',
  };
  const {
    data,
    isLoading,
    isError,
  } = usePostQuery({
    queryKey: ['/jobMela/list', JSON.stringify(payload)],
    payload,

  });



  if (isError) {
    toast.error(data.message)
  }


  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">
      <Loader className="h-8 w-8 animate-spin" />
    </div>;
  }

  const columns: ColumnDef<JobMelaItem>[] = [

    {
      accessorKey: "SL No",
      header: "SL No",
      cell: (info) => info.row.index + 1,
    },

    {
      accessorKey: "venueName",
      header: "Venue Name",
    },
    {
      accessorKey: "address",
      header: "Address",
    },

    {
      accessorKey: "district",
      header: "District",
    },

    {
      accessorKey: "start_date",
      header: "Start Date",
      cell: info => dayjs(info.getValue() as string).format("DD/MM/YYYY"),
    },
    {
      accessorKey: "end_date",
      header: "End Date ",
      cell: info => dayjs(info.getValue() as string).format("DD/MM/YYYY"),
    },

    {
      accessorKey: "bActive",
      header: "Active",
      cell: info => (
        <span className={info.getValue() === 1 ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
          {info.getValue() === 1 ? "Active" : "Inactive"}

        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex justify-center items-center gap-1">
          <button
            onClick={() => navigate(`/dashboard/jobmela/${row.original.melaId}`)}
            className="text-blue-600 hover:text-blue-800"
            title="View Details"
          >
            <Eye size={18} />
          </button>
        </div>
      ),
    },
  ];

  return (

    isError ? <div className='max-w-7xl mx-auto py-4 px-6'>
      <div className="text-center py-12">
        <NotFound />
      </div>

    </div> :
      <div className=''>
        <h1 className="text-2xl font-bold text-gray-900 mb-2"> List of Job Melas </h1>

        <div className=" bg-white px-6 py-4  flex items-center justify-between border-gray-200">
          <div >
            <SearchBar onSearch={(query) => setSearchQuery(query)} />
          </div>
          <div className="flex items-center gap-1">
            <Multiselectfilter />
            <Button variant={"destructive"} 
            onClick={() => navigate('/dashboard/jobmela/add')}
            size="sm" className="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
              <span>Add Job Mela </span>
            </Button>
          </div>

        </div>
        <DataTable<JobMelaItem>
          columns={columns}
          data={data?.data.jobMelas || []}
          pageCount={data?.data.pagination.totalPages || 0}
          pageIndex={pageIndex}
          pageSize={PAGE_SIZE}
          onPageChange={(newPageIndex) => {
            setPageIndex(newPageIndex);
          }}
        />
      </div>
  );
};
