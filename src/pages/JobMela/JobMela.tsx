import { useState } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/ui/datatable';
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom';
import { Eye } from 'lucide-react';
import type { JobMelaItem } from '@/lib/types';
import { usePostQuery } from '@/hooks/useFetchQuery';
import { NotFound } from '@/components/notfound';
import { toast } from 'sonner';
const PAGE_SIZE = 5;

export const JobMela = () => {
    const navigate = useNavigate()
  const [pageIndex, setPageIndex] = useState(0);
  const payload = {
  page: pageIndex + 1,
  limit: PAGE_SIZE,
  melaId: null,
  active: null,
  melaName: '',
  district: '',
};
const {
  data,
  isLoading,
  isError,
} = usePostQuery({
  queryKey: ['/jobMela/list', JSON.stringify(payload)],
  payload,
  staleTime: 1000 * 60 * 5,
});

  if(isError){
    toast.error(data.message)
  }


  if(isLoading){
    return <div>Loading...</div>;
  }

  const columns: ColumnDef<JobMelaItem>[] = [

    {
      accessorKey: "SL No",
      header: "SL No",
      cell: (info) => info.row.index + 1,
    },

    {
      accessorKey: "venueName",
      header: "Venue Name",
    },
    {
      accessorKey: "address",
      header: "Address",
    },

    {
      accessorKey: "district",
      header: "District",
    },

    {
      accessorKey: "start_date",
      header: "Start Date",
      cell: info => dayjs(info.getValue() as string).format("DD/MM/YYYY"),
    },
    {
      accessorKey: "end_date",
      header: "End Date ",
      cell: info => dayjs(info.getValue() as string).format("DD/MM/YYYY"),
    },

    {
      accessorKey: "bActive",
      header: "Active",
      cell: info => (
        <span className={info.getValue() === 1 ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
          {info.getValue() === 1 ? "Active" : "Inactive"}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <button
          onClick={() => navigate(`/dashboard/jobmela/${row.original.melaId}`)}
          className="text-blue-600 hover:text-blue-800"
          title="View Details"
        >
          <Eye size={18} />
        </button>
      ),
    },
  ];

  return (

    isError ? <div className='max-w-7xl mx-auto py-4 px-6'>
        <div className="text-center py-12">
        <NotFound/>
        </div>
        
    </div> :
      <div className=''>
        <h1 className="text-2xl font-bold text-gray-900 mb-2"> List of Job Melas </h1>
        <DataTable<JobMelaItem>
          columns={columns}
          data={data?.data.jobMelas || []}
          pageCount={data?.data.pagination.totalPages || 0}
          pageIndex={pageIndex}
          pageSize={PAGE_SIZE}
          onPageChange={(newPageIndex) => {
            setPageIndex(newPageIndex);
          }}
        />
      </div>
  );
};
